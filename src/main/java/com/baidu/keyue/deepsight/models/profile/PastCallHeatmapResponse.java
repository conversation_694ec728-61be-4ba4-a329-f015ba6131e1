package com.baidu.keyue.deepsight.models.profile;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName WeeklyHourlyDataResponse
 * @Description 用户每周每小时数据响应
 * <AUTHOR>
 * @Date 2025/8/12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PastCallHeatmapResponse {

    /**
     * 热力图数据，格式：[[day_index, hour, value], ...]
     * day_index: 0-6(周一到周日)，hour: 0-23
     * value: 对应的数值（未接通率或小秘书接通数）
     */
    private List<List<Object>> heatMapData;
}
