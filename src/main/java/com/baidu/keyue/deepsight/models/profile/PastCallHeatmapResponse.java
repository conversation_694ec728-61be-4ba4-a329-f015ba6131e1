package com.baidu.keyue.deepsight.models.profile;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * @ClassName PastCallHeatmapResponse
 * @Description 用户历史通话热力图数据响应
 * <AUTHOR>
 * @Date 2025/8/12
 */
@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PastCallHeatmapResponse {

    /**
     * 热力图数据（7天×24小时矩阵）
     * 外层List表示星期（0=周日，6=周六）
     * 内层List表示小时（0-23）
     */
    private List<List<Object>> heatmapData;

    /**
     * 小时标签列表
     */
    private final List<String> hourLabels = IntStream.range(0, 24)
            .mapToObj(h -> String.format("%02d:00", h)).toList();

    /**
     * 星期标签列表
     */
    private final List<String> dayLabels = Arrays.asList(
            "周日", "周一", "周二", "周三", "周四", "周五", "周六");

    /**
     * 从JSON数组数据构造热力图响应
     * @param jsonData JSON格式的数组数据 [[day_index, hour, value], ...]
     */
    public PastCallHeatmapResponse(List<List<Object>> jsonData) {
        // 初始化7天×24小时的矩阵，默认值为0
        heatmapData = IntStream.range(0, 7)
                .mapToObj(i -> IntStream.range(0, 24)
                        .mapToObj(j -> (Object) 0)
                        .collect(Collectors.toList()))
                .collect(Collectors.toList());

        // 填充实际数据
        if (jsonData != null) {
            for (List<Object> item : jsonData) {
                if (item.size() >= 3) {
                    int dayIndex = ((Number) item.get(0)).intValue();
                    int hour = ((Number) item.get(1)).intValue();
                    Object value = item.get(2);

                    // 确保索引在有效范围内
                    if (dayIndex >= 0 && dayIndex < 7 && hour >= 0 && hour < 24) {
                        heatmapData.get(dayIndex).set(hour, value);
                    }
                }
            }
        }
    }
}
