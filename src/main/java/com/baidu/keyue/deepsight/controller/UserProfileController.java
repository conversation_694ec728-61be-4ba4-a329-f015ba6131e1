package com.baidu.keyue.deepsight.controller;

import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.models.memory.UserMemoryDetailRequest;
import com.baidu.keyue.deepsight.models.memory.UserMemoryDetailResponse;
import com.baidu.keyue.deepsight.models.profile.BasicProfile;
import com.baidu.keyue.deepsight.models.profile.BasicProfileRequest;
import com.baidu.keyue.deepsight.models.profile.RecordOfCall;
import com.baidu.keyue.deepsight.models.profile.RecordOfCallRequest;
import com.baidu.keyue.deepsight.models.profile.RecordOfKeyue;
import com.baidu.keyue.deepsight.models.profile.RecordOfKeyueRequest;
import com.baidu.keyue.deepsight.models.profile.UserAnalysisRequest;
import com.baidu.keyue.deepsight.models.profile.WeeklyHourlyDataResponse;
import com.baidu.keyue.deepsight.service.memory.MemoryService;
import com.baidu.keyue.deepsight.service.user.UserProfileService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.Cookie;

/**
 * 客户档案查询API
 * @className: UserProfileController
 * @description: 客户档案查询API
 * @author: lvtao03
 * @date: 2025/02/11 14:24
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/deepsight/v1/profile")
public class UserProfileController {

    @Autowired
    private MemoryService memoryService;

    @Autowired
    private UserProfileService userProfileService;

    /**
     * 基础信息查询
     *
     * @return BaseResponse 通用返回
     */
    @RequestMapping(value = "/basicInfo", method = RequestMethod.POST)
    public BaseResponse<BasicProfile> dataSourceDetail(@RequestBody @Valid BasicProfileRequest request, HttpServletRequest httpServletRequest) {
        StringBuilder cookieHeader = new StringBuilder();
        Cookie[] cookies = httpServletRequest.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                cookieHeader.append(cookie.getName()).append("=").append(cookie.getValue()).append("; ");
            }
        }

        // 删除最后一个分号和空格
        if (!cookieHeader.isEmpty()) {
            cookieHeader.setLength(cookieHeader.length() - 2);
        }

        if (StringUtils.isNotBlank(request.getOneId())) {
            return BaseResponse.of(userProfileService.queryUserBasicProfileWithOneId(request.getOneId(), cookieHeader.toString()));
        } else if (StringUtils.isNotBlank(request.getUserId())) {
            return BaseResponse.of(userProfileService.queryUserBasicProfile(request.getUserId(), cookieHeader.toString()));
        } else {
            return BaseResponse.of(ErrorCode.BAD_REQUEST);
        }
    }

    /**
     * 行为轨迹-外呼触达记录查询
     *
     * @param request 分页查询请求
     * @return BasePageResponse 分页通用返回
     */
    @RequestMapping(value = "/recordOfCall", method = RequestMethod.POST)
    public BaseResponse<RecordOfCall> recordOfCall(@RequestBody @Valid RecordOfCallRequest request) {
        if (StringUtils.isNotBlank(request.getOneId())) {
            return BaseResponse.of(userProfileService.recordOfCallWithOneId(request.getOneId(), 
                    request.getPageNo(), request.getPageSize()));
        } else if (StringUtils.isNotBlank(request.getUserId())) {
            return BaseResponse.of(userProfileService.recordOfCall(request.getUserId(),
                    request.getPageNo(), request.getPageSize()));
        } else {
            return BaseResponse.of(ErrorCode.BAD_REQUEST);
        }
    }

    /**
     * 行为轨迹-客服对话内容记录查询
     *
     * @param request 分页查询请求
     * @return BasePageResponse 分页通用返回
     */
    @RequestMapping(value = "/recordOfKeyue", method = RequestMethod.POST)
    public BaseResponse<RecordOfKeyue> recordOfKeyue(@RequestBody @Valid RecordOfKeyueRequest request) {
        if (StringUtils.isNotBlank(request.getOneId())) {
            return BaseResponse.of(userProfileService.recordOfKeyueWithOneId(request.getOneId(), 
                    request.getPageNo(), request.getPageSize()));
        } else if (StringUtils.isNotBlank(request.getUserId())) {
            return BaseResponse.of(userProfileService.recordOfKeyue(request.getUserId(), 
                    request.getPageNo(), request.getPageSize()));
        } else {
            return BaseResponse.of(ErrorCode.BAD_REQUEST);
        }
    }

    /**
     * 用户记忆查询
     * @param request 分页查询请求
     * @return BasePageResponse 分页通用返回
     */
    @RequestMapping(value = "/memory", method = RequestMethod.POST)
    public BaseResponse<UserMemoryDetailResponse> userMemoryQuery(@RequestBody @Valid UserMemoryDetailRequest request) {
        return BaseResponse.of(memoryService.queryUserMemoryDetail(request));
    }

    /**
     * 用户未接通率分析-热力图
     *
     * @param request 查询请求
     * @return BaseResponse 通用返回
     */
    @RequestMapping(value = "/analysis/lost-call-rate", method = RequestMethod.POST)
    public BaseResponse<WeeklyHourlyDataResponse> getLostCallRateAnalysis(@RequestBody @Valid UserAnalysisRequest request) {
        return BaseResponse.of(userProfileService.getWeeklyHourlyLostCallsRate(request.getOneId()));
    }

    /**
     * 用户小秘书接通数统计-热力图
     *
     * @param request 查询请求
     * @return BaseResponse 通用返回
     */
    @RequestMapping(value = "/analysis/auto-answer-calls", method = RequestMethod.POST)
    public BaseResponse<WeeklyHourlyDataResponse> getAutoAnswerCallsAnalysis(@RequestBody @Valid UserAnalysisRequest request) {
        return BaseResponse.of(userProfileService.getWeeklyHourlyAutoAnswerCalls(request.getOneId()));
    }


}
