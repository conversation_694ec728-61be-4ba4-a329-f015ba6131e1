package com.baidu.keyue.deepsight.service.user;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.exception.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.FilterTypeEnum;
import com.baidu.keyue.deepsight.enums.FuncEnum;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.datamanage.request.FieldShowConfigQueryRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableContentListRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableFieldRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.FieldShowConfigResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.TableFieldDetailResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.baidu.keyue.deepsight.models.echopath.EchoPathResp;
import com.baidu.keyue.deepsight.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.profile.BasicOneIdRequest;
import com.baidu.keyue.deepsight.models.profile.BasicProfile;
import com.baidu.keyue.deepsight.models.profile.BasicProfileValue;
import com.baidu.keyue.deepsight.models.profile.OneIdGraph;
import com.baidu.keyue.deepsight.models.profile.OneIdGraphItem;
import com.baidu.keyue.deepsight.models.profile.RecordOfCall;
import com.baidu.keyue.deepsight.models.profile.RecordOfKeyue;
import com.baidu.keyue.deepsight.models.profile.PastCallHeatmapResponse;
import com.baidu.keyue.deepsight.models.profile.TaskSchedule;
import com.baidu.keyue.deepsight.models.profile.UserProfileDto;
import com.baidu.keyue.deepsight.models.rules.dto.RuleFilter;
import com.baidu.keyue.deepsight.models.rules.response.FilterEnumInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroup;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRule;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRuleCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.Label;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.IdMappingRuleMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.service.customer.CustomerGroupService;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.keyue.deepsight.service.datamanage.impl.TableRecordCommonService;
import com.baidu.keyue.deepsight.service.label.LabelService;
import com.baidu.keyue.deepsight.service.rules.RuleManagerService;
import com.baidu.keyue.deepsight.utils.DatetimeUtils;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.keyue.deepsight.utils.MobileProcessUtils;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.baidu.keyue.deepsight.utils.RestTemplateUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserProfileService {
    @Autowired
    private UserService userService;

    @Autowired
    private RuleManagerService ruleManagerService;

    @Autowired
    private DataTableManageService dataTableManageService;

    @Autowired
    private DorisService dorisService;

    @Autowired
    private LabelService labelService;
    @Autowired
    private CustomerGroupService customerGroupService;
    @Autowired
    private TableRecordCommonService tableRecordCommonService;

    @Autowired
    private IdMappingRuleMapper idMappingRuleMapper;

    @Autowired
    private TableFieldMetaInfoMapper tableFieldMetaMapper;

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private RestTemplateUtils restTemplateUtils;

    @Value("${echoPath.url}")
    private String echoPathHost;

    @Value("${echoPath.targetPath}")
    private String echoPathServ;

    @Value("${echoPath.dependentUris.customerGroupTaskListUri}")
    private String customerGroupTaskListUri;

    public String generateDataSyncLockKey(String... keys) {
        return StringUtils.join(keys, "-");
    }

    public BasicProfile queryUserBasicProfile(String userId, String cookie) {
        String tenantId = WebContextHolder.getTenantId();
        BasicProfile basicProfile = new BasicProfile();

        // mock_user info
        Map<String, Object> userData = userService.queryUserById(userId, tenantId);

        basicProfile.setUserInfo(buildBasicInfo(tenantId, userData));
        basicProfile.setLabels(buildLabelInfo(tenantId, userData));
        List<CustomerGroup> customerGroups = buildCustomerGroup(tenantId, userData);
        List<String> customerGroupNames = customerGroups.stream().map(CustomerGroup::getCustomerGroupName).collect(Collectors.toList());
        List<Long> customerGroupIds = customerGroups.stream().map(CustomerGroup::getId).collect(Collectors.toList());
        basicProfile.setGroups(customerGroupNames);
        basicProfile.setTaskSchedules(buildTaskSchedules(tenantId, customerGroupIds, cookie));
        if (Objects.nonNull(userData.get(Constants.TABLE_USER_ONE_ID))) {
            basicProfile.setOneIdGraph(
                    explainOneId(new BasicOneIdRequest(String.valueOf(userData.get(Constants.TABLE_USER_ONE_ID)))));
        }
        return basicProfile;
    }

    public BasicProfile queryUserBasicProfileWithOneId(String oneId, String cookie) {
        String tenantId = WebContextHolder.getTenantId();
        BasicProfile basicProfile = new BasicProfile();

        // mock_user info
        Map<String, Object> userData = userService.queryUserByOneId(oneId, tenantId);

        basicProfile.setUserInfo(buildBasicInfo(tenantId, userData));
        basicProfile.setLabels(buildLabelInfo(tenantId, userData));
        List<CustomerGroup> customerGroups = buildCustomerGroup(tenantId, userData);
        List<String> customerGroupNames = customerGroups.stream().map(CustomerGroup::getCustomerGroupName).collect(Collectors.toList());
        List<Long> customerGroupIds = customerGroups.stream().map(CustomerGroup::getId).collect(Collectors.toList());
        basicProfile.setGroups(customerGroupNames);
        basicProfile.setTaskSchedules(buildTaskSchedules(tenantId, customerGroupIds, cookie));
        basicProfile.setOneIdGraph(
                explainOneId(new BasicOneIdRequest(String.valueOf(userData.get(Constants.TABLE_USER_ONE_ID)))));
        return basicProfile;
    }

    private List<BasicProfileValue> buildBasicInfo(String tenantId, Map<String, Object> tableContent) {
        List<BasicProfileValue> basicUserInfo = Lists.newArrayList();

        Map<String, Object> originalTableContent = new HashMap<>();
        Map<String, String> predictFieldMap = new HashMap<>();

        for (Map.Entry<String, Object> entry : tableContent.entrySet()) {
            String fieldEnName = entry.getKey();
            if (!StringUtils.startsWith(fieldEnName, Constants.USER_MERGE_PREDICT_FIELD_PREFIX) && Objects.nonNull(entry.getValue())) {
                originalTableContent.put(fieldEnName, entry.getValue());
            }
        }

        for (Map.Entry<String, String> entry : Constants.MERGE_PREDICT_FIELDS_MAP.entrySet()) {
            String fieldEnName = entry.getKey();
            String originalFieldEnName = entry.getValue();
            if (StringUtils.isNotBlank(originalFieldEnName)
                    && Objects.nonNull(tableContent.get(fieldEnName))
                    && StringUtils.isNotBlank(tableContent.get(fieldEnName).toString())
            ) {
                originalTableContent.put(originalFieldEnName, tableContent.get(fieldEnName));
                predictFieldMap.put(originalFieldEnName, fieldEnName);
            }
        }

        // user basic info
        String userTable = TenantUtils.generateMockUserTableName(tenantId);
        DataTableInfo tableDetail = dataTableManageService.getTableDetailWithTableName(userTable);
        if (Objects.isNull(tableDetail)) {
            return basicUserInfo;
        }

        List<VisibleFieldResponse> visibleFieldResponse = dataTableManageService.getVisibleFields(tableDetail.getId(), false);
        if (CollectionUtils.isEmpty(visibleFieldResponse)) {
            return basicUserInfo;
        }

        // 加密字段 & 密钥
        Map<String, String> fieldEncryptInfo = tableRecordCommonService.getEncryptFields(tableDetail.getId());
        // 枚举字段
        Map<String, Map<String, String>> enumFields = visibleFieldResponse.stream()
                .filter(field -> CollectionUtils.isNotEmpty(field.getEnums()))
                .collect(Collectors.toMap(VisibleFieldResponse::getEnName, field ->
                                field.getEnums().stream().collect(Collectors.toMap(FilterEnumInfo::getKey, FilterEnumInfo::getValue, (k1, k2) -> k2)),
                        (k1, k2) -> k2
                ));

        for (VisibleFieldResponse fieldResponse : visibleFieldResponse) {
            String enName = fieldResponse.getEnName();
            String cnName = fieldResponse.getCnName();
            Object content = originalTableContent.get(enName);
            if (Objects.isNull(content)) {
                continue;
            }
            boolean predictField = predictFieldMap.containsKey(enName);
            if (Constants.TABLE_MOBILE_FIELD.equals(enName)) {
                basicUserInfo.add(new BasicProfileValue(enName, cnName, decryptMobile(content.toString(), fieldEncryptInfo), false));
                continue;
            }

            if (enumFields.containsKey(enName) && !predictField) {
                // 枚举是否映射
                if (StringUtils.isNotEmpty(enumFields.get(enName).get(content))) {
                    basicUserInfo.add(new BasicProfileValue(enName, cnName, enumFields.get(enName).get(content), predictField));
                }
            } else {
                basicUserInfo.add(new BasicProfileValue(enName, cnName, getFieldShowValue(content), predictField));
            }
        }
        return basicUserInfo;
    }

    private List<String> buildLabelInfo(String tenantId, Map<String, Object> tableContent) {
        List<Long> labelFieldIds = Lists.newArrayList();
        for (String key : tableContent.keySet()) {
            if (StringUtils.startsWith(key, Constants.DORIS_LABEL_PROCESS_FIELD_NAME_PREFIX)) {
                String content = String.valueOf(tableContent.get(key));
                if (content.length() > 2) {
                    labelFieldIds.add(Long.parseLong(StringUtils.substringAfterLast(key, "_")));
                }
            }
        }

        List<Label> labels = labelService.retrieveLabelWithFieldIds(labelFieldIds, tenantId);
        return labels.stream().map(Label::getLabelName).collect(Collectors.toList());
    }

    private List<CustomerGroup> buildCustomerGroup(String tenantId, Map<String, Object> tableContent) {
        List<Long> groupIds = Lists.newArrayList();
        for (String key : tableContent.keySet()) {
            if (StringUtils.startsWith(key, Constants.DORIS_CUSTOMER_GROUP_FIELD_PREFIX) && Objects.nonNull(tableContent.get(key))) {
                try {
                    String content = String.valueOf(tableContent.get(key));
                    if (StringUtils.isBlank(content)) {
                        continue;
                    }
                    if ("1".equals(content)) {
                        groupIds.add(Long.parseLong(StringUtils.substringAfterLast(key, "_")));
                    }
                } catch (Exception e) {
                    log.error("parse customer group value error, key: {}, value: {}", key, tableContent.get(key), e);
                }
            }
        }
        return customerGroupService.retrieveCustomerGroupWithIds(groupIds, tenantId);
    }

    private List<TaskSchedule> buildTaskSchedules(String tenantId, List<Long> customerGroupIds, String cookie) {
        if (customerGroupIds.isEmpty()) {
            return null;
        }
        String url = echoPathHost + echoPathServ + customerGroupTaskListUri;
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put(Constants.REQUEST_TENANT_ID_FIELD, tenantId);
        headerMap.put("Cookie", cookie);

        EchoPathResp echoPathResp = restTemplateUtils.postJsonEchoPath(
                url, headerMap, JsonUtils.transferToJson(customerGroupIds));
        return JsonUtils.toObject(
                JsonUtils.transferToJson(echoPathResp.getData()),
                new TypeReference<List<TaskSchedule>>() {}
        );
    }

    private String getFieldShowValue(Object value) {
        if (Objects.isNull(value)) {
            return StringUtils.EMPTY;
        }
        if (value instanceof LocalDateTime dateTime) {
            return DatetimeUtils.formatDate(dateTime);
        }
        return String.valueOf(value);
    }

    public RecordOfCall recordOfCall(String userId, Integer pageNo, Integer pageSize) {
        String tenantId = WebContextHolder.getTenantId();
        String mobile = userService.getUserMobileByUserId(tenantId, userId);
        if (StringUtils.isBlank(mobile)) {
            return new RecordOfCall(Lists.newArrayList(), BasePageResponse.Page.of(0, 0, 0L, null),
                    new FieldShowConfigResponse());
        }
        return queryRecordSessionTable(tenantId, "mobile", mobile, pageNo, pageSize);
    }

    public RecordOfCall recordOfCallWithOneId(String oneId, Integer pageNo, Integer pageSize) {
        String tenantId = WebContextHolder.getTenantId();
        return queryRecordSessionTable(tenantId, Constants.TABLE_USER_ONE_ID, oneId, pageNo, pageSize);
    }

    private RecordOfCall queryRecordSessionTable(
            String tenantId, String fieldName, String value, Integer pageNo, Integer pageSize) {
        RecordOfCall response = new RecordOfCall();

        String sessionTable = TenantUtils.generateAiobSessionTableName(tenantId);
        DataTableInfo tableDetail = dataTableManageService.getTableDetailWithTableName(sessionTable);
        if (Objects.isNull(tableDetail)) {
            return response;
        }

        // 字段
        BasePageResponse.Page<TableFieldDetailResponse> fieldPage =
                dataTableManageService.getTableFieldList(new GetTableFieldRequest(tableDetail.getId(), 1, null, 100), false);
        response.setFields(fieldPage.getResults());

        // 行数据
        GetTableContentListRequest contentRequest = new GetTableContentListRequest();
        contentRequest.setDataTableId(tableDetail.getId());
        contentRequest.setPageNo(pageNo);
        contentRequest.setPageSize(pageSize);

        TableFieldMetaInfo field = dataTableManageService.queryTableFieldMetaInfo(tableDetail.getId(), fieldName);
        if (Objects.isNull(field)) {
            return response;
        }

        RuleFilter rule = new RuleFilter();
        rule.setType(FilterTypeEnum.STRING);
        rule.setFieldId(field.getId());
        rule.setFunction(FuncEnum.EQUALS);
        rule.setParams(Lists.newArrayList(StringEscapeUtils.escapeSql(value)));
        contentRequest.setFilters(Lists.newArrayList(rule));

        BasePageResponse.Page<Map<String, String>> contentPage = dataTableManageService.getTableContent(contentRequest, false);
        response.setContents(contentPage);
        // 补充显示列配置
        FieldShowConfigQueryRequest fieldShowConfigQueryRequest = new FieldShowConfigQueryRequest(tableDetail.getId());
        FieldShowConfigResponse fieldShowConfig = dataTableManageService.getFieldShowConfig(tenantId, fieldShowConfigQueryRequest);
        response.setFieldShowConfig(fieldShowConfig);
        return response;
    }

    private String decryptMobile(String mobile, Map<String, String> fieldEncryptInfo) {
        return MobileProcessUtils.maskMobileNumber(MobileProcessUtils.decryptMobile(mobile, fieldEncryptInfo));
    }

    public OneIdGraph explainOneId(BasicOneIdRequest request) {
        OneIdGraph oneIdGraph = new OneIdGraph();
        if (StringUtils.isBlank(request.getOneId())) {
            return oneIdGraph;
        }
        oneIdGraph.setOneId(request.getOneId());
        String tenantId = WebContextHolder.getTenantId();
        String mockUserTableName = TenantUtils.generateMockUserTableName(tenantId);

        // 获取所有id mapping映射字段
        IdMappingRuleCriteria idMappingRuleCriteria = new IdMappingRuleCriteria();
        idMappingRuleCriteria.setOrderByClause(Constants.ORDER_BY_PRIORITY_ASC);
        idMappingRuleCriteria.createCriteria().andTenantIdEqualTo(tenantId);
        List<IdMappingRule> idMappingRules = idMappingRuleMapper.selectByExample(idMappingRuleCriteria);

        // 此处只用过滤 doris 中的表名即可，因为doris表名唯一
        TableFieldMetaInfoCriteria tableFieldMetaInfoCriteria = new TableFieldMetaInfoCriteria();
        tableFieldMetaInfoCriteria.createCriteria().andTableEnNameEqualTo(mockUserTableName);
        List<TableFieldMetaInfo> tableFieldMetaInfos = tableFieldMetaMapper.selectByExample(tableFieldMetaInfoCriteria);
        if (CollectionUtils.isEmpty(tableFieldMetaInfos)) {
            log.warn("用户基础信息表未查到 {}", mockUserTableName);
            return oneIdGraph;
        }
        Map<String, TableFieldMetaInfo> enFieldNameMap = tableFieldMetaInfos.stream()
                .collect(Collectors.toMap(TableFieldMetaInfo::getEnField, y -> y, (v1, v2) -> v1));
        idMappingRules = idMappingRules.stream()
                .filter(idMappingRule -> enFieldNameMap.containsKey(idMappingRule.getEnField()))
                .toList();

        // 通过oneId获取用户信息
        List<Map<String, Object>> userInfoForOneId = dorisService.selectList(
                String.format("SELECT * FROM `%s` WHERE oneId = '%s' LIMIT 1", mockUserTableName, request.getOneId()));
        if (CollectionUtils.isEmpty(userInfoForOneId)) {
            return oneIdGraph;
        }
        Map<String, Object> userInfo = userInfoForOneId.get(0);
        List<OneIdGraphItem> oneIdGraphItems = idMappingRules.stream().map(idMappingRule -> {
                    String cnField = idMappingRule.getCnField();
                    OneIdGraphItem oneIdGraphItem = new OneIdGraphItem();
                    oneIdGraphItem.setName(cnField);
                    oneIdGraphItem.setPriority(idMappingRule.getPriority());
                    oneIdGraphItem.setValues(convertShowValue(userInfo.get(idMappingRule.getEnField())));
                    return oneIdGraphItem;
                })
                .filter(item -> CollectionUtils.isNotEmpty(item.getValues()))
                .toList();
        oneIdGraph.setItems(oneIdGraphItems);
        return oneIdGraph;
    }

    /**
     * 查询客服对话内容记录
     *
     * @param userId
     * @return
     */
    public RecordOfKeyue recordOfKeyue(String userId, Integer pageNo, Integer pageSize) {
        UserAuthInfo userAuthInfo = WebContextHolder.getUserAuthInfo();
        String tenantId = String.valueOf(userAuthInfo.getTenantId());
        RecordOfKeyue response = new RecordOfKeyue();

        String keyueTable = TenantUtils.generateKeyueRecordTableName(tenantId);
        DataTableInfo tableDetail = dataTableManageService.getTableDetailWithTableName(keyueTable);
        if (Objects.isNull(tableDetail)) {
            return response;
        }
        TableFieldMetaInfo field = dataTableManageService.queryTableFieldMetaInfo(tableDetail.getId(), "user_id");
        if (Objects.isNull(field)) {
            return response;
        }
        // 字段
        GetTableFieldRequest tableFieldRequest = new GetTableFieldRequest(tableDetail.getId(), 1, null,100);
        BasePageResponse.Page<TableFieldDetailResponse> fieldPage = dataTableManageService.getTableFieldList(tableFieldRequest, false);
        response.setFields(fieldPage.getResults());
        // 行数据
        GetTableContentListRequest contentRequest = new GetTableContentListRequest();
        contentRequest.setDataTableId(tableDetail.getId());
        contentRequest.setPageNo(pageNo);
        contentRequest.setPageSize(pageSize);
        RuleFilter rule = new RuleFilter();
        rule.setType(FilterTypeEnum.STRING);
        rule.setFieldId(field.getId());
        rule.setFunction(FuncEnum.EQUALS);
        rule.setParams(Lists.newArrayList(StringEscapeUtils.escapeSql(userId)));
        contentRequest.setFilters(Lists.newArrayList(rule));

        BasePageResponse.Page<Map<String, String>> contentPage = dataTableManageService.getTableContent(contentRequest, false);
        response.setContents(contentPage);
        // 补充显示列配置
        FieldShowConfigQueryRequest fieldShowConfigQueryRequest = new FieldShowConfigQueryRequest(tableDetail.getId());
        FieldShowConfigResponse fieldShowConfig = dataTableManageService.getFieldShowConfig(tenantId, fieldShowConfigQueryRequest);
        response.setFieldShowConfig(fieldShowConfig);
        return response;
    }

    public RecordOfKeyue recordOfKeyueWithOneId(String oneId, Integer pageNo, Integer pageSize) {
        UserAuthInfo userAuthInfo = WebContextHolder.getUserAuthInfo();
        String tenantId = String.valueOf(userAuthInfo.getTenantId());
        RecordOfKeyue response = new RecordOfKeyue();

        String keyueTable = TenantUtils.generateKeyueRecordTableName(tenantId);
        DataTableInfo tableDetail = dataTableManageService.getTableDetailWithTableName(keyueTable);
        if (Objects.isNull(tableDetail)) {
            return response;
        }
        TableFieldMetaInfo field = dataTableManageService.queryTableFieldMetaInfo(tableDetail.getId(), Constants.TABLE_USER_ONE_ID);
        if (Objects.isNull(field)) {
            return response;
        }
        // 字段
        GetTableFieldRequest tableFieldRequest = new GetTableFieldRequest(tableDetail.getId(), 1, null, 100);
        BasePageResponse.Page<TableFieldDetailResponse> fieldPage = dataTableManageService.getTableFieldList(tableFieldRequest, false);
        response.setFields(fieldPage.getResults());
        // 行数据
        GetTableContentListRequest contentRequest = new GetTableContentListRequest();
        contentRequest.setDataTableId(tableDetail.getId());
        contentRequest.setPageNo(pageNo);
        contentRequest.setPageSize(pageSize);
        RuleFilter rule = new RuleFilter();
        rule.setType(FilterTypeEnum.STRING);
        rule.setFieldId(field.getId());
        rule.setFunction(FuncEnum.EQUALS);
        rule.setParams(Lists.newArrayList(StringEscapeUtils.escapeSql(oneId)));
        contentRequest.setFilters(Lists.newArrayList(rule));

        BasePageResponse.Page<Map<String, String>> contentPage = dataTableManageService.getTableContent(contentRequest, false);
        response.setContents(contentPage);
        // 补充显示列配置
        FieldShowConfigQueryRequest fieldShowConfigQueryRequest = new FieldShowConfigQueryRequest(tableDetail.getId());
        FieldShowConfigResponse fieldShowConfig = dataTableManageService.getFieldShowConfig(tenantId, fieldShowConfigQueryRequest);
        response.setFieldShowConfig(fieldShowConfig);
        return response;
    }

    /**
     * 将value转换成展示值
     * @param value 数据库的值
     * @return 转化时间、数组等字段
     */
    private List<String> convertShowValue(Object value) {
        List<String> values = new ArrayList<>();
        if (Objects.isNull(value)) {
            return values;
        }

        if (value instanceof LocalDateTime dateTime) {
            values.add(DatetimeUtils.formatDate(dateTime));
        }

        if (value instanceof List list) {
            list.forEach(item -> values.add(String.valueOf(item)));
        }

        values.add(String.valueOf(value));
        return values.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }

    /**
     * 将用户基础信息表 数据 合并到 用户档案宽表
     * @param tenantId
     * @param oneId
     */
    public void mergeUserProfileByOneId(String tenantId, String oneId) {
        // get lock
        String lockKey = generateDataSyncLockKey("mergeUserProfileByOneId", "Lock", tenantId, oneId);
        RLock lock = redisson.getLock(lockKey);
        if (!lock.tryLock()) {
            log.warn("mergeUserProfileByOneId get lock failed.");
            throw new DeepSightException.BusyRequestException(ErrorCode.BUSY_REQUEST);
        }

        try {
            // 表结构查询
            String userProfileTableName = TenantUtils.generateUserProfileTableName(tenantId);
            Map<String, String> userProfileTableSchema = dorisService.describeTable(userProfileTableName);
            // 根据 oneID 查询用户基础信息表 的多行数据
            execMerge(oneId, tenantId, userProfileTableName, userProfileTableSchema);
        } catch (Exception e) {
            log.error("mergeUserProfileByOneId error ", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private void execMerge(String oneId, String tenantId, String userProfileTableName, Map<String, String> userProfileTableSchema) {
        List<Map<String, Object>> mockUserRows = userService.queryMockUserRows(oneId, tenantId);

        UserProfileDto userProfileDto = new UserProfileDto(tenantId, oneId);
        userProfileDto.mergeMockUserData(mockUserRows);

        if (userProfileDto.isEmptyMockUserData()) {
            return;
        }

        Map<String, Object> mockProfileRow = userService.queryUserProfileRow(oneId, tenantId);

        // 合并数据
        userProfileDto.mergeProfileRow(mockProfileRow);

        // update / insert 到 用户档案宽表
        String sql = userProfileDto.genSql(userProfileTableName, userProfileTableSchema);
        dorisService.execSql(sql);
    }

    public void mergeUserProfileByTenantId(String tenantId) {
        if (StringUtils.isEmpty(tenantId)) {
            return;
        }

        String userProfileTableName = TenantUtils.generateUserProfileTableName(tenantId);
        Map<String, String> userProfileTableSchema = dorisService.describeTable(userProfileTableName);

        String mockUserTableName = TenantUtils.generateMockUserTableName(tenantId);

        // load oneId set
        List<String> oneIds = Lists.newArrayList();
        String anHourAgo = DatetimeUtils.backToDate(60);
        String sql = ORMUtils.generateLoadOneIdSetWithTime(mockUserTableName, anHourAgo);
        try {
            List<Map<String, Object>> result = dorisService.selectList(sql);
            result.forEach(map -> oneIds.add(map.get(Constants.TABLE_USER_ONE_ID).toString()));
        } catch (Exception e) {
            log.error("mergeUserProfileByTenantId load oneId set error ", e);
            return;
        }

        if (CollectionUtils.isEmpty(oneIds)) {
            log.warn("mergeUserProfileByTenantId load oneId set got empty");
            return;
        }

        String lockKey = generateDataSyncLockKey("mergeUserProfileByTenantId", "Lock", tenantId);
        RLock lock = redisson.getLock(lockKey);
        if (!lock.tryLock()) {
            log.warn("mergeUserProfileByTenantId get lock failed.");
            throw new DeepSightException.BusyRequestException(ErrorCode.BUSY_REQUEST);
        }

        try {
            oneIds.forEach(oneId -> execMerge(oneId, tenantId, userProfileTableName, userProfileTableSchema));
        } catch (Exception e) {
            log.error("mergeUserProfileByTenantId error ", e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    /**
     * 获取用户每周每小时的未接通率
     *
     * @param oneId 用户oneId
     * @return PastCallHeatmapResponse
     */
    public PastCallHeatmapResponse getWeeklyHourlyLostCallsRate(String oneId) {
        String tenantId = WebContextHolder.getTenantId();
        String tableName = TenantUtils.generateUserMetricTableName(tenantId);

        String sql = String.format("SELECT %s FROM %s WHERE oneId = '%s' LIMIT 1",
                                  Constants.USER_METRIC_AGG_FIELD_LOST_RATE,
                                  tableName, StringEscapeUtils.escapeSql(oneId));

        List<Map<String, Object>> resultList = Lists.newArrayList();
        try {
            resultList = dorisService.selectList(sql);
        } catch (Exception e) {
            log.error("UserProfileService.getWeeklyHourlyLostCallsRate selectList error", e);
        }

        if (CollectionUtils.isEmpty(resultList)) {
            return new PastCallHeatmapResponse(Lists.newArrayList());
        }

        Object jsonData = resultList.get(0).get(Constants.USER_METRIC_AGG_FIELD_LOST_RATE);
        if (Objects.isNull(jsonData)) {
            return new PastCallHeatmapResponse(Lists.newArrayList());
        }

        try {
            List<List<Object>> data = JsonUtils.readType(jsonData.toString(),
                                                        new TypeReference<List<List<Object>>>() {});
            return new PastCallHeatmapResponse(data);
        } catch (Exception e) {
            log.error("解析用户未接通率热力图 JSON数据失败", e);
            return new PastCallHeatmapResponse(Lists.newArrayList());
        }
    }

    /**
     * 获取用户每周每小时的小秘书接通数统计
     *
     * @param oneId 用户oneId
     * @return PastCallHeatmapResponse
     */
    public PastCallHeatmapResponse getWeeklyHourlyAutoAnswerCalls(String oneId) {
        String tenantId = WebContextHolder.getTenantId();
        String tableName = TenantUtils.generateUserMetricTableName(tenantId);

        String sql = String.format("SELECT %s FROM %s WHERE oneId = '%s' LIMIT 1",
                                  Constants.USER_METRIC_AGG_FIELD_AUTO_ANSWER_CALLS,
                                  tableName, StringEscapeUtils.escapeSql(oneId));

        List<Map<String, Object>> resultList = Lists.newArrayList();
        try {
            resultList = dorisService.selectList(sql);
        } catch (Exception e) {
            log.error("UserProfileService.getWeeklyHourlyAutoAnswerCalls selectList error", e);
        }

        if (CollectionUtils.isEmpty(resultList)) {
            return new PastCallHeatmapResponse(Lists.newArrayList());
        }

        Object jsonData = resultList.get(0).get(Constants.USER_METRIC_AGG_FIELD_AUTO_ANSWER_CALLS);
        if (Objects.isNull(jsonData)) {
            return new PastCallHeatmapResponse(Lists.newArrayList());
        }

        try {
            List<List<Object>> data = JsonUtils.readType(jsonData.toString(),
                                                        new TypeReference<List<List<Object>>>() {});
            return new PastCallHeatmapResponse(data);
        } catch (Exception e) {
            log.error("解析用户小秘书接听数热力图 JSON数据失败", e);
            return new PastCallHeatmapResponse(Lists.newArrayList());
        }
    }
}
